/**
 * Supabase Service for GT Motorsports vehicle inventory
 * This file provides functions to interact with the Supabase database
 */
import { supabase } from '../utils/supabase';

/**
 * Get all vehicles from the vehicles table
 * @returns {Promise} Promise that resolves with an array of vehicles
 */
export const getAllVehiclesFromSupabase = async () => {
  try {
    // First check if the vehicles table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(1);
    // If the vehicles table doesn't exist, try the GTINV table
    if (tableError && tableError.code === '42P01') {
      console.log('vehicles table not found, trying GTINV table');
      return getAllVehiclesFromGTINV();
    }
    
    // Get all vehicles with their related data
    const { data, error } = await supabase
      .from('vehicles')
      .select(`
        *,
        vehicle_details(*),
        vehicle_features(feature),
        vehicle_highlights(highlight),
        vehicle_images(*)
      `);
    
    if (error) {
      console.error('Error fetching vehicles from Supabase:', error);
      throw error;
    }
    
    // Transform the data to match the expected format in the application
    const transformedData = data.map(item => {
      // Get the primary image or the first image
      const images = item.vehicle_images || [];
      
      // Transform image URLs to ensure they're publicly accessible
      const transformImageUrl = (url) => {
        if (!url) return 'https://via.placeholder.com/400x300?text=No+Image';
        
        // If the URL is already a full URL, return it as is
        if (url.startsWith('http')) return url;
        
        // If it's a relative URL or just a path, construct the full URL
        const projectId = 'wjqlfcxgrdfyqpjsbnyp';
        return `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${url}`;
      };
      
      // Check if image_path exists and use it for the primary image
      let primaryImage;
      if (item.image_path) {
        // Construct the URL using image_path which points to the storage bucket
        const projectId = 'wjqlfcxgrdfyqpjsbnyp';
        primaryImage = `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${item.image_path}`;
      } else {
        // Fall back to the old method if image_path is not available
        primaryImage = transformImageUrl(
          images.find(img => img.is_primary)?.url || (images.length > 0 ? images[0].url : null)
        );
      }
      
      // Get all image URLs for the gallery
      const gallery = images.map(img => transformImageUrl(img.url));
      
      // If we have an image_path but no gallery images, add the primary image to the gallery
      if (item.image_path && gallery.length === 0) {
        gallery.push(primaryImage);
      }
      
      // Get features and highlights
      const features = (item.vehicle_features || []).map(f => f.feature);
      const highlights = (item.vehicle_highlights || []).map(h => h.highlight);
      
      // Get details
      const details = item.vehicle_details || {};
      
      return {
        id: item.id,
        title: `${item.year} ${item.make} ${item.model} ${item.trim || ''}`.trim(),
        price: item.price || 0,
        specialPrice: item.special_price,
        image: primaryImage,
        gallery: gallery.length > 0 ? gallery : [primaryImage],
        mileage: details.mileage || 0,
        year: item.year || new Date().getFullYear(),
        make: item.make || '',
        model: item.model || '',
        trim: item.trim || '',
        doors: details.doors || 0,
        bodyStyle: details.body_style || '',
        engine: details.engine || '',
        engineSize: details.engine_size || '',
        drivetrain: details.drivetrain || '',
        transmission: details.transmission || '',
        exteriorColor: details.exterior_color || '',
        interiorColor: details.interior_color || '',
        passengers: details.passengers || 0,
        fuelType: details.fuel_type || '',
        cityFuel: details.city_fuel || '',
        hwyFuel: details.hwy_fuel || '',
        stockNumber: item.stock_number || `GT-${Math.floor(Math.random() * 10000)}`,
        vin: item.vin || '',
        highlights: highlights,
        features: features,
        description: item.description || ''
      };
    });
    
    console.log(`Loaded ${transformedData.length} vehicles from Supabase`);
    return transformedData;
  } catch (error) {
    console.error('Error in getAllVehiclesFromSupabase:', error);
    throw error;
  }
};

// New functions for body style filtering
export const getVehiclesByBodyStyle = async (bodyStyle) => {
  try {
    // First check if the vehicles table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(1);

    // If the vehicles table doesn't exist, try the GTINV table
    if (tableError?.code === '42P01') {
      console.log('vehicles table not found, trying GTINV table');
      return getVehiclesByBodyStyleFromGTINV(bodyStyle);
    }

    // Get vehicles with their related data filtered by body style
    const { data, error } = await supabase
      .from('vehicles')
      .select(
        '*,' +
        'vehicle_details(*),' +
        'vehicle_features(feature),' +
        'vehicle_highlights(highlight),' +
        'vehicle_images(*)'
      )
      .eq('vehicle_details.body_style', bodyStyle);

    if (error) throw error;

    // Transform the data to match the expected format
    const transformedData = data.map(item => ({
      id: item.id,
      title: `${item.year} ${item.make} ${item.model} ${item.trim || ''}`.trim(),
      price: item.price || 0,
      image: item.image_path ? 
        `https://wjqlfcxgrdfyqpjsbnyp.supabase.co/storage/v1/object/public/car-images/${item.image_path}` : 
        'https://via.placeholder.com/400x300?text=No+Image',
      bodyStyle: item.vehicle_details?.body_style || '',
    }));

    return transformedData;
  } catch (error) {
    console.error('Error fetching vehicles by body style:', error);
    throw error;
  }
};

const getVehiclesByBodyStyleFromGTINV = async (bodyStyle) => {
  try {
    const { data, error } = await supabase
      .from('GTINV')
      .select('*')
      .eq('bodyStyle', bodyStyle);

    if (error) throw error;

    return data.map(item => ({
      id: item.id,
      title: `${item.year} ${item.make} ${item.model} ${item.trim || ''}`.trim(),
      price: item.price || 0,
      image: item.image_path ? 
        `https://wjqlfcxgrdfyqpjsbnyp.supabase.co/storage/v1/object/public/car-images/${item.image_path}` : 
        'https://via.placeholder.com/400x300?text=No+Image',
      bodyStyle: item.bodyStyle || ''
    }));
  } catch (error) {
    console.error('Error fetching from GTINV by body style:', error);
    throw error;
  }
};

/** 
 * Fallback function to get vehicles from the GTINV table
 * @returns {Promise} Promise that resolves with an array of vehicles
 */
const getAllVehiclesFromGTINV = async () => {
  try {
    const { data, error } = await supabase.from('GTINV').select('*');
    
    if (error) {
      console.error('Error fetching vehicles from GTINV table:', error);
      throw error;
    }
    
    // Transform the data to match the expected format in the application
    const transformedData = data.map(item => {
      // Get the image URL from image_path if it exists
      const getImageUrl = (imagePath) => {
        if (!imagePath) return 'https://via.placeholder.com/400x300?text=No+Image';
        
        // If it's already a full URL, return it
        if (imagePath.startsWith('http')) return imagePath;
        
        // Construct the full URL using the storage bucket
        const projectId = 'wjqlfcxgrdfyqpjsbnyp';
        return `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${imagePath}`;
      };

      // Generate a title from the year, make, and model
      const title = `${item.year} ${item.make} ${item.model} ${item.trim || ''}`.trim();
      
      // Get the image URL from image_path
      const imageUrl = getImageUrl(item.image_path);
      
      return {
        id: item.id,
        title: title,
        price: item.price || 0,
        specialPrice: null,
        image: imageUrl,
        gallery: [imageUrl], // Use the same image for gallery if no others exist
        mileage: item.mileage || 0,
        year: item.year || new Date().getFullYear(),
        make: item.make || '',
        model: item.model || '',
        trim: item.trim || '',
        doors: 0,
        bodyStyle: item.bodyStyle || '',
        engine: item.engine || '',
        engineSize: '',
        drivetrain: item.driveTrain || '',
        transmission: '',
        exteriorColor: item.exteriorColor || '',
        interiorColor: item.interiorColor || '',
        passengers: 0,
        fuelType: '',
        cityFuel: '',
        hwyFuel: '',
        stockNumber: item.stockNumber || `GT-${Math.floor(Math.random() * 10000)}`,
        vin: '',
        highlights: [],
        features: [],
        description: ''
      };
    });
    
    console.log(`Loaded ${transformedData.length} vehicles from GTINV table`);
    return transformedData;
  } catch (error) {
    console.error('Error in getAllVehiclesFromGTINV:', error);
    throw error;
  }
};

/**
 * Get a vehicle by ID
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle data
 */
export const getVehicleByIdFromSupabase = async (id) => {
  try {
    // First check if the vehicles table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(1);
    
    // If the vehicles table doesn't exist, try the GTINV table
    if (tableError && tableError.code === '42P01') {
      console.log('vehicles table not found, trying GTINV table');
      return getVehicleByIdFromGTINV(id);
    }
    
    // Get the vehicle with its related data
    const { data, error } = await supabase
      .from('vehicles')
      .select(
        '*,' +
        'vehicle_details(*),' +
        'vehicle_features(feature),' +
        'vehicle_highlights(highlight),' +
        'vehicle_images(*)'
      )
      .eq('id', id)
      .single();
    
    if (error) {
      console.error(`Error fetching vehicle with ID ${id} from Supabase:`, error);
      throw error;
    }
    
    if (!data) {
      return null;
    }
    
    // Get the primary image or the first image
    const images = data.vehicle_images || [];
    
    // Transform image URLs to ensure they're publicly accessible
    const transformImageUrl = (url) => {
      if (!url) return 'https://via.placeholder.com/400x300?text=No+Image';
      
      // If the URL is already a full URL, return it as is
      if (url.startsWith('http')) return url;
      
      // If it's a relative URL or just a path, construct the full URL
      const projectId = 'wjqlfcxgrdfyqpjsbnyp';
      return `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${url}`;
    };
    
    // Check if image_path exists and use it for the primary image
    let primaryImage;
    if (data.image_path) {
      // Construct the URL using image_path which points to the storage bucket
      const projectId = 'wjqlfcxgrdfyqpjsbnyp';
      primaryImage = `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${data.image_path}`;
    } else {
      // Fall back to the old method if image_path is not available
      primaryImage = transformImageUrl(
        images.find(img => img.is_primary)?.url || (images.length > 0 ? images[0].url : null)
      );
    }
    
    // Get all image URLs for the gallery
    const gallery = images.map(img => transformImageUrl(img.url));
    
    // If we have an image_path but no gallery images, add the primary image to the gallery
    if (data.image_path && gallery.length === 0) {
      gallery.push(primaryImage);
    }
    
    // Get features and highlights
    const features = (data.vehicle_features || []).map(f => f.feature);
    const highlights = (data.vehicle_highlights || []).map(h => h.highlight);
    
    // Get details
    const details = data.vehicle_details || {};
    
    // Transform the data to match the expected format in the application
    const transformedData = {
      id: data.id,
      title: `${data.year} ${data.make} ${data.model} ${data.trim || ''}`.trim(),
      price: data.price || 0,
      specialPrice: data.special_price,
      image: primaryImage,
      gallery: gallery.length > 0 ? gallery : [primaryImage],
      mileage: details.mileage || 0,
      year: data.year || new Date().getFullYear(),
      make: data.make || '',
      model: data.model || '',
      trim: data.trim || '',
      doors: details.doors || 0,
      bodyStyle: details.body_style || '',
      engine: details.engine || '',
      engineSize: details.engine_size || '',
      drivetrain: details.drivetrain || '',
      transmission: details.transmission || '',
      exteriorColor: details.exterior_color || '',
      interiorColor: details.interior_color || '',
      passengers: details.passengers || 0,
      fuelType: details.fuel_type || '',
      cityFuel: details.city_fuel || '',
      hwyFuel: details.hwy_fuel || '',
      stockNumber: data.stock_number || `GT-${Math.floor(Math.random() * 10000)}`,
      vin: data.vin || '',
      highlights: highlights,
      features: features,
      description: data.description || ''
    };
    
    return transformedData;
  } catch (error) {
    console.error(`Error in getVehicleByIdFromSupabase:`, error);
    throw error;
  }
};

/**
 * Fallback function to get a vehicle by ID from the GTINV table
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves with the vehicle data
 */
const getVehicleByIdFromGTINV = async (id) => {
  try {
    const { data, error } = await supabase
      .from('GTINV')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      console.error(`Error fetching vehicle with ID ${id} from GTINV table:`, error);
      throw error;
    }
    
    if (!data) {
      return null;
    }
    
    // Get the image URL from image_path if it exists
    const getImageUrl = (imagePath) => {
      if (!imagePath) return 'https://via.placeholder.com/400x300?text=No+Image';
      
      // If it's already a full URL, return it
      if (imagePath.startsWith('http')) return imagePath;
      
      // Construct the full URL using the storage bucket
      const projectId = 'wjqlfcxgrdfyqpjsbnyp';
      return `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${imagePath}`;
    };

    // Generate a title from the year, make, and model
    const title = `${data.year} ${data.make} ${data.model} ${data.trim || ''}`.trim();
    
    // Get the image URL from image_path
    const imageUrl = getImageUrl(data.image_path);
    
    // Transform the data to match the expected format in the application
    const transformedData = {
      id: data.id,
      title: title,
      price: data.price || 0,
      specialPrice: null,
      image: imageUrl,
      gallery: [imageUrl], // Use the same image for gallery if no others exist
      mileage: data.mileage || 0,
      year: data.year || new Date().getFullYear(),
      make: data.make || '',
      model: data.model || '',
      trim: data.trim || '',
      doors: 0,
      bodyStyle: data.bodyStyle || '',
      engine: data.engine || '',
      engineSize: '',
      drivetrain: data.driveTrain || '',
      transmission: '',
      exteriorColor: data.exteriorColor || '',
      interiorColor: data.interiorColor || '',
      passengers: 0,
      fuelType: '',
      cityFuel: '',
      hwyFuel: '',
      stockNumber: data.stockNumber || `GT-${Math.floor(Math.random() * 10000)}`,
      vin: '',
      highlights: [],
      features: [],
      description: ''
    };
    
    return transformedData;
  } catch (error) {
    console.error(`Error in getVehicleByIdFromGTINV:`, error);
    throw error;
  }
};

/**
 * Add a vehicle to the database
 * @param {Object} vehicleData - The vehicle data to add
 * @returns {Promise} Promise that resolves with the new vehicle ID
 */
export const addVehicleToSupabase = async (vehicleData) => {
  try {
    // First check if the vehicles table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(1);
    
    // If the vehicles table doesn't exist, try the GTINV table
    if (tableError && tableError.code === '42P01') {
      console.log('vehicles table not found, using GTINV table');
      return addVehicleToGTINV(vehicleData);
    }
    
    // Extract data for different tables
    const {
      // Core vehicle data
      title, year, make, model, trim, price, specialPrice, stockNumber, vin, description,
      
      // Vehicle details
      bodyStyle, doors, engine, engineSize, drivetrain, transmission,
      exteriorColor, interiorColor, passengers, fuelType, cityFuel, hwyFuel, mileage,
      
      // Arrays
      features = [], highlights = [], gallery = [], image
    } = vehicleData;
    
    // Extract image_path from the image URL if it exists
    let image_path = null;
    if (image) {
      // If the image is a full URL to the Supabase storage
      if (image.includes('supabase.co/storage/v1/object/public/car-images/')) {
        // Extract the path after car-images/
        const match = image.match(/car-images\/(.+)$/);
        if (match && match[1]) {
          image_path = match[1];
        }
      } 
      // If the image is just a path (not a full URL)
      else if (!image.startsWith('http')) {
        image_path = image;
      }
    }
    
    // 1. Add to vehicles table
    const { data: vehicleResult, error: vehicleError } = await supabase
      .from('vehicles')
      .insert({
        title,
        year,
        make,
        model,
        trim,
        price,
        special_price: specialPrice,
        stock_number: stockNumber,
        vin,
        description,
        image_path, // Store the image path
        created_at: new Date(),
        updated_at: new Date()
      })
      .select();
    
    if (vehicleError) {
      console.error('Error adding vehicle:', vehicleError);
      throw vehicleError;
    }
    
    if (!vehicleResult || vehicleResult.length === 0) {
      throw new Error('Failed to add vehicle: No ID returned');
    }
    
    const vehicleId = vehicleResult[0].id;
    
    // 2. Add to vehicle_details table
    const { error: detailsError } = await supabase
      .from('vehicle_details')
      .insert({
        vehicle_id: vehicleId,
        body_style: bodyStyle,
        doors,
        engine,
        engine_size: engineSize,
        drivetrain,
        transmission,
        exterior_color: exteriorColor,
        interior_color: interiorColor,
        passengers,
        fuel_type: fuelType,
        city_fuel: cityFuel,
        hwy_fuel: hwyFuel,
        mileage,
        odometer: mileage,
        odometer_unit: 'km'
      });
    
    if (detailsError) {
      console.error('Error adding vehicle details:', detailsError);
      // Continue anyway, we don't want to fail the whole operation
    }
    
    // 3. Add features
    if (features.length > 0) {
      const featureRows = features.map(feature => ({
        vehicle_id: vehicleId,
        feature
      }));
      
      const { error: featuresError } = await supabase
        .from('vehicle_features')
        .insert(featureRows);
      
      if (featuresError) {
        console.error('Error adding vehicle features:', featuresError);
      }
    }
    
    // 4. Add highlights
    if (highlights.length > 0) {
      const highlightRows = highlights.map(highlight => ({
        vehicle_id: vehicleId,
        highlight
      }));
      
      const { error: highlightsError } = await supabase
        .from('vehicle_highlights')
        .insert(highlightRows);
      
      if (highlightsError) {
        console.error('Error adding vehicle highlights:', highlightsError);
      }
    }
    // 5. Add images
    const allImages = [...new Set([image, ...gallery].filter(Boolean))];
    if (allImages.length > 0) {
      // Function to transform image URLs to ensure they're in the correct format
      const transformImageUrl = (url) => {
        if (!url) return null;
        
        // If the URL is already a full URL, return it as is
        if (url.startsWith('http')) return url;
        
        // If it's a relative URL or just a path, construct the full URL
        const projectId = 'wjqlfcxgrdfyqpjsbnyp';
        return `https://${projectId}.supabase.co/storage/v1/object/public/car-images/${url}`;
      };
      
      // Transform all image URLs
      const transformedImages = allImages.map(url => transformImageUrl(url)).filter(Boolean);
      
      // Create image rows for database insertion
      const imageRows = transformedImages.map((url, index) => {
        return {
          vehicle_id: vehicleId,
          url: url,
          is_primary: url === transformImageUrl(image), // First image is primary
          sort_order: index
        };
      });
      const { error: imagesError } = await supabase
        .from('vehicle_images')
        .insert(imageRows);
      
      if (imagesError) {
        console.error('Error adding vehicle images:', imagesError);
      }
    }
    
    return vehicleId;
  } catch (error) {
    console.error('Error in addVehicleToSupabase:', error);
    throw error;
  }
};

/**
 * Fallback function to add a vehicle to the GTINV table
 * @param {Object} vehicleData - The vehicle data to add
 * @returns {Promise} Promise that resolves with the new vehicle ID
 */
const addVehicleToGTINV = async (vehicleData) => {
  try {
    // Extract all fields that exist in the GTINV table
    const {
      title, year, make, model, trim, price,
      image, mileage, bodyStyle, engine, drivetrain,
      exteriorColor, interiorColor
    } = vehicleData;

    // Extract image path from the image URL if it exists
    let image_path = null;
    if (image) {
      if (image.includes('supabase.co/storage/v1/object/public/car-images/')) {
        const match = image.match(/car-images\/(.+)$/);
        if (match && match[1]) {
          image_path = match[1];
        }
      } else if (!image.startsWith('http')) {
        image_path = image;
      }
    }

    // Get the current user to extract tenant_id from metadata
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) {
      console.error('Error getting current user:', userError);
      throw new Error('Authentication required to add vehicle');
    }

    // Extract tenant_id from user metadata
    const tenant_id = user?.app_metadata?.tenant_id;
    if (!tenant_id) {
      throw new Error('No tenant_id found in user metadata. User must be properly authenticated.');
    }

    // Create the vehicle object with correct column names
    const gtinvVehicle = {
      title: title || `${year} ${make} ${model} ${trim || ''}`.trim(),
      year: year || new Date().getFullYear(),
      make: make || '',
      model: model || '',
      trim: trim || '',
      price: price || 0,
      image_path,
      mileage: mileage || 0,
      bodyStyle: bodyStyle || '',
      engine: engine || '',
      driveTrain: drivetrain || '',
      exteriorColor: exteriorColor || '',
      interiorColor: interiorColor || '',
      tenant_id: tenant_id // Add tenant_id for RLS
    };

    const { data, error } = await supabase
      .from('GTINV')
      .insert([gtinvVehicle])
      .select();

    if (error) {
      console.error('Error adding vehicle to GTINV table:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      throw new Error('Failed to add vehicle to GTINV: No data returned from insert');
    }

    const vehicleId = data[0].id;
    if (!vehicleId) {
      throw new Error('Failed to add vehicle to GTINV: No ID returned');
    }

    return vehicleId;
  } catch (error) {
    console.error('Error in addVehicleToGTINV:', error);
    throw error;
  }
};

/**
 * Update a vehicle in the database
 * @param {number} id - The vehicle ID
 * @param {Object} vehicleData - The updated vehicle data
 * @returns {Promise} Promise that resolves when the update is complete
 */
export const updateVehicleInSupabase = async (id, vehicleData) => {
  try {
    // First check if the vehicles table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(1);
    
    // If the vehicles table doesn't exist, try the GTINV table
    if (tableError && tableError.code === '42P01') {
      console.log('vehicles table not found, using GTINV table');
      return updateVehicleInGTINV(id, vehicleData);
    }
    
    // Get the existing vehicle to check if it exists
    const { data: existingVehicle, error: existingError } = await supabase
      .from('vehicles')
      .select('id')
      .eq('id', id)
      .single();
    
    if (existingError && existingError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error(`Error checking if vehicle with ID ${id} exists:`, existingError);
      throw existingError;
    }
    
    // If the vehicle doesn't exist in the vehicles table, try the GTINV table
    if (!existingVehicle) {
      console.log(`Vehicle with ID ${id} not found in vehicles table, trying GTINV table`);
      return updateVehicleInGTINV(id, vehicleData);
    }
    
    // Extract data for different tables
    const {
      // Core vehicle data
      title, year, make, model, trim, price, specialPrice, stockNumber, vin, description,
      
      // Vehicle details
      bodyStyle, doors, engine, engineSize, drivetrain, transmission,
      exteriorColor, interiorColor, passengers, fuelType, cityFuel, hwyFuel, mileage,
      
      // Arrays
      features, highlights, gallery, image
    } = vehicleData;
    
    // Extract image_path from the image URL if it exists
    let image_path = undefined;
    if (image !== undefined) {
      // If the image is a full URL to the Supabase storage
      if (image && image.includes('supabase.co/storage/v1/object/public/car-images/')) {
        // Extract the path after car-images/
        const match = image.match(/car-images\/(.+)$/);
        if (match && match[1]) {
          image_path = match[1];
        }
      } 
      // If the image is just a path (not a full URL)
      else if (image && !image.startsWith('http')) {
        image_path = image;
      }
      // If image is null or empty, set image_path to null
      else if (!image) {
        image_path = null;
      }
    }
    
    // 1. Update vehicles table
    const { error: vehicleError } = await supabase
      .from('vehicles')
      .update({
        title: title !== undefined ? title : undefined,
        year: year !== undefined ? year : undefined,
        make: make !== undefined ? make : undefined,
        model: model !== undefined ? model : undefined,
        trim: trim !== undefined ? trim : undefined,
        price: price !== undefined ? price : undefined,
        special_price: specialPrice !== undefined ? specialPrice : undefined,
        stock_number: stockNumber !== undefined ? stockNumber : undefined,
        vin: vin !== undefined ? vin : undefined,
        description: description !== undefined ? description : undefined,
        image_path: image_path, // Update the image path
        updated_at: new Date()
      })
      .eq('id', id);
    
    if (vehicleError) {
      console.error(`Error updating vehicle with ID ${id}:`, vehicleError);
      throw vehicleError;
    }
    
    // 2. Update vehicle details
    if (bodyStyle !== undefined || doors !== undefined || engine !== undefined ||
        engineSize !== undefined || drivetrain !== undefined || transmission !== undefined ||
        exteriorColor !== undefined || interiorColor !== undefined || passengers !== undefined ||
        fuelType !== undefined || cityFuel !== undefined || hwyFuel !== undefined || mileage !== undefined) {
      
      // Check if details exist
      const { data: existingDetails, error: checkError } = await supabase
        .from('vehicle_details')
        .select('*')
        .eq('vehicle_id', id)
        .single();
      
      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "not found" error
        console.error('Error checking vehicle details:', checkError);
        throw checkError;
      }
      
      const detailsData = {
        body_style: bodyStyle !== undefined ? bodyStyle : undefined,
        doors: doors !== undefined ? doors : undefined,
        engine: engine !== undefined ? engine : undefined,
        engine_size: engineSize !== undefined ? engineSize : undefined,
        drivetrain: drivetrain !== undefined ? drivetrain : undefined,
        transmission: transmission !== undefined ? transmission : undefined,
        exterior_color: exteriorColor !== undefined ? exteriorColor : undefined,
        interior_color: interiorColor !== undefined ? interiorColor : undefined,
        passengers: passengers !== undefined ? passengers : undefined,
        fuel_type: fuelType !== undefined ? fuelType : undefined,
        city_fuel: cityFuel !== undefined ? cityFuel : undefined,
        hwy_fuel: hwyFuel !== undefined ? hwyFuel : undefined,
        mileage: mileage !== undefined ? mileage : undefined,
        odometer: mileage !== undefined ? mileage : undefined,
        odometer_unit: 'km'
      };
      
      // Remove undefined values
      Object.keys(detailsData).forEach(key => {
        if (detailsData[key] === undefined) {
          delete detailsData[key];
        }
      });
      
      if (existingDetails) {
        // Update existing details
        const { error: detailsError } = await supabase
          .from('vehicle_details')
          .update(detailsData)
          .eq('vehicle_id', id);
        
        if (detailsError) {
          console.error('Error updating vehicle details:', detailsError);
          throw detailsError;
        }
      } else {
        // Create new details
        const { error: detailsError } = await supabase
          .from('vehicle_details')
          .insert({
            vehicle_id: id,
            ...detailsData
          });
        
        if (detailsError) {
          console.error('Error creating vehicle details:', detailsError);
          throw detailsError;
        }
      }
    }
    
    // 3. Update features (delete all and re-add)
    if (features && features.length > 0) {
      // Delete existing features
      const { error: deleteError } = await supabase
        .from('vehicle_features')
        .delete()
        .eq('vehicle_id', id);
      
      if (deleteError) {
        console.error('Error deleting vehicle features:', deleteError);
        throw deleteError;
      }
      
      // Add new features
      const featureRows = features.map(feature => ({
        vehicle_id: id,
        feature
      }));
      
      const { error: featuresError } = await supabase
        .from('vehicle_features')
        .insert(featureRows);
      
      if (featuresError) {
        console.error('Error adding vehicle features:', featuresError);
        throw featuresError;
      }
    }
    
    // 4. Update highlights (delete all and re-add)
    if (highlights && highlights.length > 0) {
      // Delete existing highlights
      const { error: deleteError } = await supabase
        .from('vehicle_highlights')
        .delete()
        .eq('vehicle_id', id);
      
      if (deleteError) {
        console.error('Error deleting vehicle highlights:', deleteError);
        throw deleteError;
      }
      
      // Add new highlights
      const highlightRows = highlights.map(highlight => ({
        vehicle_id: id,
        highlight
      }));
      
      const { error: highlightsError } = await supabase
        .from('vehicle_highlights')
        .insert(highlightRows);
      
      if (highlightsError) {
        console.error('Error adding vehicle highlights:', highlightsError);
        throw highlightsError;
      }
    }
    
    // 5. Update images (delete all and re-add)
    if ((gallery && gallery.length > 0) || image) {
      // Delete existing images
      const { error: deleteError } = await supabase
        .from('vehicle_images')
        .delete()
        .eq('vehicle_id', id);
      
      if (deleteError) {
        console.error('Error deleting vehicle images:', deleteError);
        throw deleteError;
      }
      
      // Add new images
      const allImages = [...new Set([image, ...(gallery || [])].filter(Boolean))];
      if (allImages.length > 0) {
        const imageRows = allImages.map((url, index) => ({
          vehicle_id: id,
          url,
          is_primary: url === image, // First image is primary
          sort_order: index
        }));
        
        const { error: imagesError } = await supabase
          .from('vehicle_images')
          .insert(imageRows);
        
        if (imagesError) {
          console.error('Error adding vehicle images:', imagesError);
          throw imagesError;
        }
      }
    }
    
    return id;
  } catch (error) {
    console.error(`Error in updateVehicleInSupabase:`, error);
    throw error;
  }
};

/**
 * Fallback function to update a vehicle in the GTINV table
 * @param {number} id - The vehicle ID
 * @param {Object} vehicleData - The updated vehicle data
 * @returns {Promise} Promise that resolves when the update is complete
 */
const updateVehicleInGTINV = async (id, vehicleData) => {
  try {
    // Extract all fields that exist in the GTINV table
    const {
      year, make, model, trim, price, specialPrice,
      bodyStyle, engine, drivetrain, exteriorColor, interiorColor, mileage,
      doors, passengers, engineSize, transmission, fuelType, cityFuel, hwyFuel,
      stockNumber, vin, highlights, features, description,
      image, gallery, title
    } = vehicleData;
    
    // Create an update object with all fields that exist in the GTINV table
    const updateData = {};
    
    if (title !== undefined) updateData.title = title;
    if (year !== undefined) updateData.year = year;
    if (make !== undefined) updateData.make = make;
    if (model !== undefined) updateData.model = model;
    if (trim !== undefined) updateData.trim = trim;
    if (price !== undefined) updateData.price = price;
    if (specialPrice !== undefined) updateData.specialPrice = specialPrice;
    if (bodyStyle !== undefined) updateData.bodyStyle = bodyStyle;
    if (engine !== undefined) updateData.engine = engine;
    if (drivetrain !== undefined) updateData.driveTrain = drivetrain;
    if (exteriorColor !== undefined) updateData.exteriorColor = exteriorColor;
    if (interiorColor !== undefined) updateData.interiorColor = interiorColor;
    if (mileage !== undefined) updateData.mileage = mileage;
    if (doors !== undefined) updateData.doors = doors;
    if (passengers !== undefined) updateData.passengers = passengers;
    if (engineSize !== undefined) updateData.engineSize = engineSize;
    if (transmission !== undefined) updateData.transmission = transmission;
    if (fuelType !== undefined) updateData.fuelType = fuelType;
    if (cityFuel !== undefined) updateData.cityFuelEconomy = cityFuel;
    if (hwyFuel !== undefined) updateData.highwayFuelEconomy = hwyFuel;
    if (stockNumber !== undefined) updateData.stockNumber = stockNumber;
    if (vin !== undefined) updateData.vin = vin;
    if (highlights !== undefined) updateData.highlights = highlights;
    if (features !== undefined) updateData.features = features;
    if (description !== undefined) updateData.description = description;
    
    // No need to handle images as there's no image column in the database
    
    console.log(`Updating vehicle with ID ${id} in GTINV table with data:`, updateData);
    
    const { error } = await supabase
      .from('GTINV')
      .update(updateData)
      .eq('id', id);
    
    if (error) {
      console.error(`Error updating vehicle with ID ${id} in GTINV table:`, error);
      throw error;
    }
    
    return id;
  } catch (error) {
    console.error(`Error in updateVehicleInGTINV:`, error);
    throw error;
  }
};

/**
 * Delete a vehicle from the database
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves when the delete is complete
 */
export const deleteVehicleFromSupabase = async (id) => {
  try {
    // First check if the vehicles table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(1);
    
    // If the vehicles table doesn't exist, try the GTINV table
    if (tableError && tableError.code === '42P01') {
      console.log('vehicles table not found, using GTINV table');
      return deleteVehicleFromGTINV(id);
    }
    
    // Get the existing vehicle to check if it exists
    const { data: existingVehicle, error: existingError } = await supabase
      .from('vehicles')
      .select('id')
      .eq('id', id)
      .single();
    
    if (existingError && existingError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error(`Error checking if vehicle with ID ${id} exists:`, existingError);
      throw existingError;
    }
    
    // If the vehicle doesn't exist in the vehicles table, try the GTINV table
    if (!existingVehicle) {
      console.log(`Vehicle with ID ${id} not found in vehicles table, trying GTINV table`);
      return deleteVehicleFromGTINV(id);
    }
    
    // Delete related data first
    await Promise.all([
      supabase.from('vehicle_details').delete().eq('vehicle_id', id),
      supabase.from('vehicle_features').delete().eq('vehicle_id', id),
      supabase.from('vehicle_highlights').delete().eq('vehicle_id', id),
      supabase.from('vehicle_images').delete().eq('vehicle_id', id)
    ]);
    
    // Delete the vehicle
    const { error } = await supabase
      .from('vehicles')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error(`Error deleting vehicle with ID ${id}:`, error);
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error(`Error in deleteVehicleFromSupabase:`, error);
    throw error;
  }
};

/**
 * Fallback function to delete a vehicle from the GTINV table
 * @param {number} id - The vehicle ID
 * @returns {Promise} Promise that resolves when the delete is complete
 */
const deleteVehicleFromGTINV = async (id) => {
  try {
    const { error } = await supabase
      .from('GTINV')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error(`Error deleting vehicle with ID ${id} from GTINV table:`, error);
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error(`Error in deleteVehicleFromGTINV:`, error);
    throw error;
  }
};

/**
 * Initialize the Supabase database
 * This function checks if the necessary tables exist and creates them if they don't
 * @returns {Promise} Promise that resolves when the database is initialized
 */
export const initSupabaseDatabase = async () => {
  try {
    // Check if the vehicles table exists
    const { data: tableCheck, error: tableError } = await supabase
      .from('vehicles')
      .select('id')
      .limit(1);
    
    // If the vehicles table doesn't exist, check if the GTINV table exists
    if (tableError && tableError.code === '42P01') {
      console.log('vehicles table not found, checking GTINV table');
      
      const { data: gtinvCheck, error: gtinvError } = await supabase
        .from('GTINV')
        .select('id')
        .limit(1);
      
      if (gtinvError && gtinvError.code === '42P01') {
        console.log('GTINV table not found, creating it');
        
        // Create the GTINV table
        const { error: createError } = await supabase.rpc('create_gtinv_table');
        
        if (createError) {
          console.error('Error creating GTINV table:', createError);
          console.log('Table creation via RPC failed, this is expected in development. Tables should be created manually in the Supabase dashboard.');
        }
      } else {
        console.log('GTINV table exists');
      }
    } else {
      console.log('vehicles table exists');
    }
    
    return true;
  } catch (error) {
    console.error('Error initializing Supabase database:', error);
    return false;
  }
};

export default {
  getAllVehiclesFromSupabase,
  getVehicleByIdFromSupabase,
  addVehicleToSupabase,
  updateVehicleInSupabase,
  deleteVehicleFromSupabase,
  initSupabaseDatabase
};
