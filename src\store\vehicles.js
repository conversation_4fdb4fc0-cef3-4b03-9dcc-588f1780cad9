import { ref, computed } from 'vue'
import database from '../database' // Local DB fallback
import supabaseService from '../database/supabaseService'
import { supabase } from '../utils/supabase' // Import supabase client for direct queries

// --- Core State ---
const vehicles = ref([]);
const isLoading = ref(false); // Controls global loading state (skeletons)
const isInitialized = ref(false);
const useSupabase = ref(true); // Default to Supabase

// --- Pagination State ---
const currentPage = ref(1);
const pageSize = ref(20); // Items per page - keep at a reasonable size for performance
const totalVehicles = ref(0); // Total count from backend
const hasMoreVehicles = ref(true); // If true, observer will try to load more
const lastPageLoaded = ref(false); // Track if we've loaded the last page

// --- Filter State ---
const activeFilters = ref({
  search: '',
  priceRange: [0, 300000],
  kilometersRange: [0, 500000],
  yearRange: [1900, new Date().getFullYear() + 1], // Wider year range to include all vehicles
  make: [],
  model: [],
  color: [],
  bodyType: [],
  transmission: [],
  fuelType: [],
  sortOption: 'newest' // Default sort
});

// --- Initialization ---
const initStore = async () => {
  if (isInitialized.value) {
    console.log('[Store] Already initialized.');
    return;
  }
  console.log('[Store] Initializing...');
  isLoading.value = true;
  isInitialized.value = false; // Mark as not ready yet
  vehicles.value = []; // Clear existing vehicles on init
  currentPage.value = 1; // Reset page
  totalVehicles.value = 0;
  hasMoreVehicles.value = true; // Assume more initially

  try {
    if (useSupabase.value) {
      try {
        console.log('[Store] Initializing Supabase connection...');
        await supabaseService.initSupabaseDatabase(); // Checks table existence
        console.log('[Store] Loading first page from Supabase...');

        // Reset loading state before calling loadNextPage
        isLoading.value = false;

        await loadNextPage(); // Load page 1
        isInitialized.value = true;
        console.log('[Store] Initialization complete (Supabase).');
      } catch (supabaseError) {
        console.error('[Store] Supabase initialization/load failed, falling back to local:', supabaseError);
        useSupabase.value = false;
        await initLocalDatabase(); // Fallback
      }
    } else {
      await initLocalDatabase(); // Use local if specified
    }
  } catch (error) {
    console.error('[Store] Error during initialization:', error);
    hasMoreVehicles.value = false; // Stop loading on major init error
  } finally {
     // isLoading is set to false within loadNextPage's finally block
     // or after initLocalDatabase completes.
     if (!isLoading.value && !isInitialized.value) {
         // If somehow init failed without setting isLoading false
         isLoading.value = false;
         isInitialized.value = true; // Mark as initialized even if empty/failed
         console.log('[Store] Finalizing init: isLoading=false, isInitialized=true');
     }
  }
};

// --- Load Next Page (CRITICAL FUNCTION) - IMPROVED ---
const loadNextPage = async () => {
  console.log('[Store] loadNextPage called with current state:', {
    currentPage: currentPage.value,
    pageSize: pageSize.value,
    totalVehicles: totalVehicles.value,
    loadedVehicles: vehicles.value.length,
    hasMoreVehicles: hasMoreVehicles.value,
    isLoading: isLoading.value,
    lastPageLoaded: lastPageLoaded.value
  });

  // Prevent loading if we know there are no more vehicles or already loaded last page
  if (!hasMoreVehicles.value || lastPageLoaded.value) {
    console.log('[Store] loadNextPage skipped: hasMoreVehicles is false or lastPageLoaded is true.');
    isLoading.value = false; // Ensure loading stops if called when no more
    return;
  }

  if (!useSupabase.value) {
    console.log('[Store] loadNextPage skipped: Not using Supabase.');
    hasMoreVehicles.value = false; // No pagination for local DB in this setup
    lastPageLoaded.value = true;
    isLoading.value = false;
    return;
  }

  // Check if already loading - but don't skip on first page
  if (isLoading.value && currentPage.value > 1) {
    console.log('[Store] loadNextPage skipped: Already loading and not first page.');
    return;
  }

  // Set loading state specifically for this operation
  // Note: `isLoading` controls the main skeleton view
  isLoading.value = true;
  console.log(`[Store] Loading page ${currentPage.value} (pageSize: ${pageSize.value})...`);

  // Track retry attempts
  let retryCount = 0;
  const maxRetries = 2;

  const attemptLoad = async () => {
    try {
      // Calculate expected range for debugging
      const startIndex = (currentPage.value - 1) * pageSize.value;
      const endIndex = startIndex + pageSize.value - 1;
      console.log(`[Store] Expected range for this request: ${startIndex} to ${endIndex} (inclusive)`);

      // Ensure the sort option is explicitly passed to the backend
      console.log(`[Store] Requesting page ${currentPage.value} with sort option: ${activeFilters.value.sortOption}`);
      
      const { vehicles: pageVehicles, totalCount } = await supabaseService.getVehiclesFromSupabase({
        page: currentPage.value,
        pageSize: pageSize.value,
        filters: activeFilters.value, // Pass the current filters to the backend
        sortOption: activeFilters.value.sortOption // Explicitly pass sort option for clarity
      });

      console.log(`[Store] Received from Supabase: ${pageVehicles?.length || 0} vehicles, totalCount: ${totalCount}`);

      // --- Process Results ---
      // Only update total count if it's valid and greater than current
      if (totalCount !== undefined && totalCount !== null) {
        if (totalCount > totalVehicles.value || totalVehicles.value === 0) {
          totalVehicles.value = totalCount;
          console.log(`[Store] Updated totalVehicles to ${totalCount}`);
        } else if (totalCount < totalVehicles.value) {
          console.warn(`[Store] Received totalCount (${totalCount}) is less than current totalVehicles (${totalVehicles.value}). This may indicate a query issue.`);
          // Still update to be consistent with backend
          totalVehicles.value = totalCount;
        }
      }

      // Handle case where no vehicles are returned
      if (!pageVehicles || pageVehicles.length === 0) {
        console.log('[Store] No vehicles returned for this page.');

        // If this is the first page and no vehicles, likely no matching results
        if (currentPage.value === 1) {
          console.log('[Store] First page returned no vehicles. Likely no matching results for current filters.');
          vehicles.value = []; // Clear any existing vehicles
        }

        // Mark as no more vehicles and last page loaded
        hasMoreVehicles.value = false;
        lastPageLoaded.value = true;
        console.log('[Store] Setting hasMoreVehicles=false and lastPageLoaded=true');
        return;
      }

      // Process received vehicles
      // Log the IDs of received vehicles for debugging
      console.log('[Store] Received vehicle IDs:', pageVehicles.map(v => v.id));

      // Append new vehicles, ensuring no duplicates from overlapping calls
      const existingIds = new Set(vehicles.value.map(v => v.id));
      console.log('[Store] Existing vehicle IDs:', Array.from(existingIds));

      const newUniqueVehicles = pageVehicles.filter(v => !existingIds.has(v.id));
      console.log('[Store] New unique vehicle IDs:', newUniqueVehicles.map(v => v.id));

      // Check if we got any new vehicles
      if (newUniqueVehicles.length === 0) {
        console.log('[Store] No new unique vehicles received. This may indicate duplicate data or pagination issues.');

        // If we're on a page beyond the first and got no new vehicles, consider it the last page
        if (currentPage.value > 1) {
          hasMoreVehicles.value = false;
          lastPageLoaded.value = true;
          console.log('[Store] No new vehicles on subsequent page. Setting hasMoreVehicles=false and lastPageLoaded=true');
        }

        // Still continue processing to update pagination state
      }

      // Pre-process and normalize vehicle data
      newUniqueVehicles.forEach(vehicle => {
        // Normalize image paths
        if (!vehicle.image) {
          vehicle.image = '/REDGTWHEEL.png';
        }

        // Ensure gallery is always an array
        if (!Array.isArray(vehicle.gallery)) {
          vehicle.gallery = [];
        }
        
        // Enhanced normalization for numeric fields to ensure proper sorting
        // Convert mileage to number with improved handling
        if (vehicle.mileage !== undefined && vehicle.mileage !== null) {
          // Remove any non-numeric characters (like commas or units)
          let cleanMileage = String(vehicle.mileage).replace(/[^\d.-]/g, '');
          // Handle empty string or just decimal point
          if (cleanMileage === '' || cleanMileage === '.') cleanMileage = '0';
          vehicle.mileage = Number(cleanMileage) || 0;
          
          // Log suspicious values for debugging
          if (vehicle.mileage > 0 && vehicle.mileage < 100) {
            console.warn(`[Store] Suspiciously low mileage for vehicle ID ${vehicle.id}: ${vehicle.mileage} (original: ${vehicle.mileage})`);
          } else if (vehicle.mileage > 500000) {
            console.warn(`[Store] Suspiciously high mileage for vehicle ID ${vehicle.id}: ${vehicle.mileage} (original: ${vehicle.mileage})`);
          }
        } else {
          vehicle.mileage = 0;
        }
        
        // Convert price to number with improved handling
        if (vehicle.price !== undefined && vehicle.price !== null) {
          // Remove any non-numeric characters (like currency symbols)
          let cleanPrice = String(vehicle.price).replace(/[^\d.-]/g, '');
          // Handle empty string or just decimal point
          if (cleanPrice === '' || cleanPrice === '.') cleanPrice = '0';
          vehicle.price = Number(cleanPrice) || 0;
          
          // Log suspicious values for debugging
          if (vehicle.price > 0 && vehicle.price < 1000) {
            console.warn(`[Store] Suspiciously low price for vehicle ID ${vehicle.id}: ${vehicle.price} (original: ${vehicle.price})`);
          } else if (vehicle.price > 200000) {
            console.warn(`[Store] Suspiciously high price for vehicle ID ${vehicle.id}: ${vehicle.price} (original: ${vehicle.price})`);
          }
        } else {
          vehicle.price = 0;
        }
        
        // Convert year to number with improved handling
        if (vehicle.year !== undefined && vehicle.year !== null) {
          const yearNum = Number(vehicle.year) || 0;
          vehicle.year = yearNum;
          
          // Log suspicious values for debugging
          const currentYear = new Date().getFullYear();
          if (yearNum > 0 && yearNum < 1900) {
            console.warn(`[Store] Suspiciously low year for vehicle ID ${vehicle.id}: ${yearNum}`);
          } else if (yearNum > currentYear + 1) {
            console.warn(`[Store] Suspiciously high year for vehicle ID ${vehicle.id}: ${yearNum}`);
          }
        } else {
          vehicle.year = 0;
        }
      });
      
      // Log any vehicles with unusual mileage values for debugging
      const unusualMileage = newUniqueVehicles.filter(v =>
        v.mileage < 500 && v.mileage > 0 || v.mileage > 500000
      );
      
      if (unusualMileage.length > 0) {
        console.log('[Store] Vehicles with unusual mileage values:');
        unusualMileage.forEach(v => {
          console.log(`  ID: ${v.id}, Year: ${v.year}, Make: ${v.make}, Model: ${v.model}, Mileage: ${v.mileage}`);
        });
      }

      // Update the vehicles array
      if (currentPage.value === 1) {
        // First page - replace all vehicles
        vehicles.value = newUniqueVehicles;
        console.log('[Store] First page - replaced all vehicles');
      } else {
        // Subsequent page - append new vehicles
        // Add a small delay before updating the store to allow DOM to stabilize
        await new Promise(resolve => setTimeout(resolve, 100));

        // Combine existing and new vehicles
        const combinedVehicles = [...vehicles.value, ...newUniqueVehicles];

        // Apply client-side sorting as a fallback to ensure consistent results
        console.log(`[Store] Applying client-side sorting for option: ${activeFilters.value.sortOption || 'newest'}`);
        
        // Sort the vehicles based on the current sort option
        const sortedVehicles = [...combinedVehicles]; // Create a copy to sort
        
        switch (activeFilters.value.sortOption) {
          case 'newest':
            sortedVehicles.sort((a, b) => (b.year || 0) - (a.year || 0));
            break;
          case 'oldest':
            sortedVehicles.sort((a, b) => (a.year || 0) - (b.year || 0));
            break;
          case 'price-high':
            sortedVehicles.sort((a, b) => (b.price || 0) - (a.price || 0));
            break;
          case 'price-low':
            sortedVehicles.sort((a, b) => (a.price || 0) - (b.price || 0));
            break;
          case 'mileage-low':
            sortedVehicles.sort((a, b) => (a.mileage || 0) - (b.mileage || 0));
            break;
          case 'mileage-high':
            sortedVehicles.sort((a, b) => (b.mileage || 0) - (a.mileage || 0));
            break;
          default:
            // Default to newest first
            sortedVehicles.sort((a, b) => (b.year || 0) - (a.year || 0));
        }
        
        // Log the first few vehicles to help with debugging
        if (sortedVehicles.length > 0) {
          console.log(`[Store] First 3 vehicles after client-side sorting (${activeFilters.value.sortOption}):`);
          sortedVehicles.slice(0, 3).forEach(v => {
            console.log(`  ID: ${v.id}, Year: ${v.year}, Make: ${v.make}, Model: ${v.model}, Price: ${v.price}, Mileage: ${v.mileage}`);
          });
        }

        vehicles.value = sortedVehicles; // Update with sorted list
        console.log('[Store] Subsequent page - appended new vehicles. Total now:', vehicles.value.length);
      }

      console.log(`[Store] Added ${newUniqueVehicles.length} new vehicles. Total in store: ${vehicles.value.length}`);

      // --- Update Pagination State ---
      // Calculate if there are more based on total count and current position
      const loadedCount = vehicles.value.length;
      const previousHasMore = hasMoreVehicles.value;

      // Check if we've loaded all vehicles or if the current page returned fewer than pageSize
      const receivedLessThanPageSize = pageVehicles.length < pageSize.value;

      // More robust check for determining if there are more vehicles
      if (totalCount !== undefined && totalCount !== null) {
        // If we have a total count, use it to determine if there are more
        hasMoreVehicles.value = loadedCount < totalCount;
        console.log(`[Store] Using totalCount to determine hasMoreVehicles: ${loadedCount} < ${totalCount} = ${hasMoreVehicles.value}`);
      } else {
        // If no total count, use the page size check
        hasMoreVehicles.value = !receivedLessThanPageSize;
        console.log(`[Store] Using page size to determine hasMoreVehicles: !${receivedLessThanPageSize} = ${hasMoreVehicles.value}`);
      }

      // If we received fewer than page size, we've likely reached the end
      if (receivedLessThanPageSize) {
        lastPageLoaded.value = true;
        console.log('[Store] Received fewer than pageSize vehicles. Setting lastPageLoaded=true');
      }

      console.log(`[Store] Pagination check: loadedCount=${loadedCount}, totalVehicles=${totalVehicles.value}, receivedLessThanPageSize=${receivedLessThanPageSize}, hasMoreVehicles=${hasMoreVehicles.value} (was ${previousHasMore}), lastPageLoaded=${lastPageLoaded.value}`);

      // Increment page number ONLY if there are potentially more vehicles
      if (hasMoreVehicles.value && !lastPageLoaded.value) {
        const previousPage = currentPage.value;
        currentPage.value++;
        console.log(`[Store] Incremented currentPage from ${previousPage} to ${currentPage.value}`);
      } else {
        console.log(`[Store] Reached end or fetched last page. Not incrementing currentPage (stays at ${currentPage.value}).`);
      }

    } catch (error) {
      console.error(`[Store] Error loading page ${currentPage.value}:`, error);

      // Check for 416 Range Not Satisfiable error
      const is416Error = error.message && (
        error.message.includes('416') ||
        error.message.includes('Requested range not satisfiable') ||
        (error.code === '416')
      );

      if (is416Error) {
        console.log('[Store] Detected 416 Range Not Satisfiable error - no more results available for current filters');
        // Update pagination state to indicate no more vehicles
        hasMoreVehicles.value = false;
        lastPageLoaded.value = true;
        
        // If this happens on the first page, clear vehicles
        if (currentPage.value === 1) {
          vehicles.value = [];
        }
        
        // Don't retry for 416 errors
        isLoading.value = false;
        return;
      }

      // Retry logic for other transient errors
      if (retryCount < maxRetries) {
        retryCount++;
        console.log(`[Store] Retrying (${retryCount}/${maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
        return attemptLoad(); // Recursive retry
      }

      // Reset loading state immediately on error after retries exhausted
      isLoading.value = false;
      console.log('[Store] Set isLoading to false due to error after retries');

      // Don't set hasMoreVehicles to false on error to allow manual retry
      throw new Error(`Failed to load vehicles after ${maxRetries} attempts: ${error.message || 'Unknown error'}`);
    }
  };

  try {
    await attemptLoad();
  } finally {
    // Use a timeout to ensure skeletons show briefly if needed
    // Only set loading to false if it hasn't been set by the catch block
    if (isLoading.value) {
      setTimeout(() => {
        isLoading.value = false;
        console.log('[Store] Set isLoading to false after loadNextPage attempt.');
      }, 200);
    }
  }
};


// --- Local Database Fallback ---
const initLocalDatabase = async () => {
  console.log('[Store] Initializing local database...');
  isLoading.value = true;
  try {
    await database.initDatabase();
    const dbVehicles = await database.getAllVehicles();
    vehicles.value = dbVehicles;
    totalVehicles.value = dbVehicles.length; // Set total for local
    hasMoreVehicles.value = false; // No pagination for local DB
    isInitialized.value = true;
    console.log(`[Store] Loaded ${dbVehicles.length} vehicles from local database.`);
  } catch (error) {
      console.error('[Store] Error initializing local database:', error);
      hasMoreVehicles.value = false;
  } finally {
      isLoading.value = false;
      console.log('[Store] Finished local database init.');
  }
};

// --- Getters ---
// Use computed for direct access, but ensure initStore is called if needed
const getAllVehicles = computed(() => {
  // This computed is less useful now with pagination,
  // the component should use `vehicles.value` directly.
  // If accessed before init, trigger init.
  if (!isInitialized.value && !isLoading.value) {
      console.warn('[Store] getAllVehicles accessed before init, triggering initStore...');
      initStore(); // Fire and forget, UI relies on isLoading/vehicles
  }
  return vehicles.value;
});

// --- Actions ---
const getVehicleById = async (id) => {
  console.log(`[Store] getVehicleById called for ID: ${id}`);
  if (!isInitialized.value) {
    console.log('[Store] Store not initialized, awaiting initStore...');
    await initStore();
  }

  const vehicleIdNum = typeof id === 'string' ? parseInt(id, 10) : id;
  const localVehicle = vehicles.value.find(v => v.id === vehicleIdNum || v.id === id);

  if (localVehicle) {
    console.log(`[Store] Found vehicle ID ${id} in local cache.`);
    return localVehicle;
  }

  console.log(`[Store] Vehicle ID ${id} not in cache, fetching from ${useSupabase.value ? 'Supabase' : 'local DB'}...`);
  try {
    let vehicle;
    if (useSupabase.value) {
      vehicle = await supabaseService.getVehicleByIdFromSupabase(id);
    } else {
      vehicle = await database.getVehicleById(id); // Assumes local DB has this method
    }

    if (vehicle) {
      console.log(`[Store] Fetched vehicle ID ${id}. Adding to cache if missing.`);
      // Add to cache if fetched successfully and not already present
      const exists = vehicles.value.some(v => v.id === vehicle.id);
      if (!exists) {
          vehicles.value.push(vehicle); // Consider sorting or placement if order matters
      }
    } else {
        console.log(`[Store] Vehicle ID ${id} not found in data source.`);
    }
    return vehicle;
  } catch (error) {
    console.error(`[Store] Error fetching vehicle ID ${id}:`, error);
    return null;
  }
};

// --- CUD Actions (Keep existing logic, ensure they interact with correct source) ---
const addVehicle = async (vehicle) => {
    if (!isInitialized.value) await initStore();
    console.log('[Store] Adding vehicle...');
    try {
        let vehicleId;
        if (useSupabase.value) {
            vehicleId = await supabaseService.addVehicleToSupabase(vehicle);
        } else {
            vehicleId = await database.addVehicle(vehicle);
        }
        if (!vehicleId) throw new Error('Failed to add vehicle: No ID returned');
        const newVehicle = { ...vehicle, id: vehicleId };
        vehicles.value.push(newVehicle); // Add to local state
        totalVehicles.value++; // Increment total count
        console.log('[Store] Vehicle added successfully, ID:', vehicleId);
        
        // Trigger CarPages export after successfully adding a vehicle
        try {
            // Import the auto-export utility dynamically to avoid circular dependencies
            const { triggerCarPagesExport, isAutoExportEnabled } = await import('../utils/autoExport');
            
            // Check if auto-export is enabled before triggering
            if (isAutoExportEnabled()) {
                // Check if we've exported recently (throttle to prevent too many exports)
                const lastExportTime = localStorage.getItem('lastAutoExportTime');
                const now = Date.now();
                const minTimeBetweenExports = 5 * 60 * 1000; // 5 minutes in milliseconds
                
                if (!lastExportTime || (now - parseInt(lastExportTime)) > minTimeBetweenExports) {
                    console.log('[Store] Auto-export is enabled, triggering CarPages export...');
                    // Store the current time as the last export time
                    localStorage.setItem('lastAutoExportTime', now.toString());
                    
                    // Trigger the export asynchronously (don't await) to avoid blocking the UI
                    triggerCarPagesExport().then(result => {
                        console.log('[Store] Auto-export result:', result);
                    }).catch(exportError => {
                        console.error('[Store] Auto-export error:', exportError);
                        // Don't throw the error to avoid affecting the vehicle addition flow
                    });
                } else {
                    console.log('[Store] Auto-export throttled - last export was less than 5 minutes ago');
                }
            } else {
                console.log('[Store] Auto-export is disabled, skipping export');
            }
        } catch (exportError) {
            // Log the error but don't throw it to avoid affecting the vehicle addition flow
            console.error('[Store] Error triggering auto-export:', exportError);
        }
        
        return newVehicle;
    } catch (error) {
        console.error('[Store] Error adding vehicle:', error);
        throw error;
    }
};

const updateVehicle = async (id, updatedData) => {
    if (!isInitialized.value) await initStore();
    console.log(`[Store] Updating vehicle ID: ${id}...`);
    try {
        if (useSupabase.value) {
            await supabaseService.updateVehicleInSupabase(id, updatedData);
        } else {
            await database.updateVehicle(id, updatedData);
        }
        const index = vehicles.value.findIndex(v => v.id === id || v.id === String(id)); // Handle string/number ID
        if (index !== -1) {
            vehicles.value[index] = { ...vehicles.value[index], ...updatedData }; // Merge data
            console.log(`[Store] Updated vehicle ID ${id} in local cache.`);
            
            // Trigger CarPages export after successfully updating a vehicle
            // Only if significant fields that would affect CarPages listing are updated
            const significantFieldsUpdated = checkSignificantVehicleChanges(updatedData);
            if (significantFieldsUpdated) {
                try {
                    // Import the auto-export utility dynamically to avoid circular dependencies
                    const { triggerCarPagesExport, isAutoExportEnabled } = await import('../utils/autoExport');
                    
                    // Check if auto-export is enabled before triggering
                    if (isAutoExportEnabled()) {
                        // Check if we've exported recently (throttle to prevent too many exports)
                        const lastExportTime = localStorage.getItem('lastAutoExportTime');
                        const now = Date.now();
                        const minTimeBetweenExports = 5 * 60 * 1000; // 5 minutes in milliseconds
                        
                        if (!lastExportTime || (now - parseInt(lastExportTime)) > minTimeBetweenExports) {
                            console.log('[Store] Auto-export is enabled, triggering CarPages export after update...');
                            // Store the current time as the last export time
                            localStorage.setItem('lastAutoExportTime', now.toString());
                            
                            // Trigger the export asynchronously (don't await) to avoid blocking the UI
                            triggerCarPagesExport().then(result => {
                                console.log('[Store] Auto-export result after update:', result);
                            }).catch(exportError => {
                                console.error('[Store] Auto-export error after update:', exportError);
                            });
                        } else {
                            console.log('[Store] Auto-export throttled - last export was less than 5 minutes ago');
                        }
                    } else {
                        console.log('[Store] Auto-export is disabled, skipping export after update');
                    }
                } catch (exportError) {
                    // Log the error but don't throw it to avoid affecting the vehicle update flow
                    console.error('[Store] Error triggering auto-export after update:', exportError);
                }
            } else {
                console.log('[Store] No significant fields updated, skipping auto-export');
            }
            
            return vehicles.value[index];
        } else {
            console.warn(`[Store] Updated vehicle ID ${id} not found in local cache after update.`);
            // Optionally fetch it again to add to cache
            return await getVehicleById(id);
        }
    } catch (error) {
        console.error(`[Store] Error updating vehicle ID ${id}:`, error);
        throw error;
    }
};

/**
 * Check if the updated data contains significant changes that would affect CarPages listing
 * @param {Object} updatedData - The data being updated
 * @returns {boolean} - Whether significant fields were updated
 */
const checkSignificantVehicleChanges = (updatedData) => {
    // Fields that would significantly affect a CarPages listing
    const significantFields = [
        'price', 'specialPrice', 'title', 'year', 'make', 'model', 'trim',
        'mileage', 'vin', 'stockNumber', 'bodyStyle', 'transmission',
        'drivetrain', 'exteriorColor', 'interiorColor', 'fuelType',
        'engine', 'features', 'description', 'image', 'gallery'
    ];
    
    // Check if any significant fields are being updated
    return significantFields.some(field => updatedData[field] !== undefined);
};

const deleteVehicle = async (id) => {
    if (!isInitialized.value) await initStore();
    console.log(`[Store] Deleting vehicle ID: ${id}...`);
    try {
        if (useSupabase.value) {
            await supabaseService.deleteVehicleFromSupabase(id);
        } else {
            await database.deleteVehicle(id);
        }
        const index = vehicles.value.findIndex(v => v.id === id || v.id === String(id));
        if (index !== -1) {
            vehicles.value.splice(index, 1);
            totalVehicles.value--; // Decrement total count
            console.log(`[Store] Deleted vehicle ID ${id} from local cache.`);
            
            // Trigger CarPages export after successfully deleting a vehicle
            try {
                // Import the auto-export utility dynamically to avoid circular dependencies
                const { triggerCarPagesExport, isAutoExportEnabled } = await import('../utils/autoExport');
                
                // Check if auto-export is enabled before triggering
                if (isAutoExportEnabled()) {
                    // Check if we've exported recently (throttle to prevent too many exports)
                    const lastExportTime = localStorage.getItem('lastAutoExportTime');
                    const now = Date.now();
                    const minTimeBetweenExports = 5 * 60 * 1000; // 5 minutes in milliseconds
                    
                    if (!lastExportTime || (now - parseInt(lastExportTime)) > minTimeBetweenExports) {
                        console.log('[Store] Auto-export is enabled, triggering CarPages export after deletion...');
                        // Store the current time as the last export time
                        localStorage.setItem('lastAutoExportTime', now.toString());
                        
                        // Trigger the export asynchronously (don't await) to avoid blocking the UI
                        triggerCarPagesExport().then(result => {
                            console.log('[Store] Auto-export result after deletion:', result);
                        }).catch(exportError => {
                            console.error('[Store] Auto-export error after deletion:', exportError);
                        });
                    } else {
                        console.log('[Store] Auto-export throttled - last export was less than 5 minutes ago');
                    }
                } else {
                    console.log('[Store] Auto-export is disabled, skipping export after deletion');
                }
            } catch (exportError) {
                // Log the error but don't throw it to avoid affecting the vehicle deletion flow
                console.error('[Store] Error triggering auto-export after deletion:', exportError);
            }
        }
        return true;
    } catch (error) {
        console.error(`[Store] Error deleting vehicle ID ${id}:`, error);
        throw error;
    }
};

const vinLookup = async (vin) => { /* ... keep existing ... */ };

// --- Filter Management ---
const updateFilters = async (newFilters) => {
  console.log('[Store] Updating filters:', newFilters);

  // Check if filters have significantly changed
  const significantChange = checkSignificantFilterChange(activeFilters.value, newFilters);
  
  // Check specifically for bodyType changes since they're important
  const bodyTypeChanged = !arraysEqual(activeFilters.value.bodyType, newFilters.bodyType);
  
  // Log detailed filter change information
  if (bodyTypeChanged) {
    console.log('[Store] Body type filter changed:', {
      old: activeFilters.value.bodyType,
      new: newFilters.bodyType
    });
  }

  // Update the filters
  activeFilters.value = { ...newFilters };

  if (significantChange) {
    console.log('[Store] Significant filter change detected, resetting pagination and reloading');
    // Reset pagination
    currentPage.value = 1;
    vehicles.value = []; // Clear existing vehicles
    hasMoreVehicles.value = true; // Reset pagination state
    lastPageLoaded.value = false; // Reset last page loaded flag
    totalVehicles.value = 0; // Reset total count to ensure accurate new count

    // Reload with new filters - removing the setTimeout that was causing issues
    try {
      await loadNextPage();
      console.log('[Store] Successfully loaded first page after filter change');
    } catch (error) {
      console.error('[Store] Error loading vehicles after filter change:', error);
      // If there's an error, make sure we're not stuck in a loading state
      isLoading.value = false;
    }
  }

  return vehicles.value;
};

// Helper to check if filters have changed significantly enough to warrant a reload
const checkSignificantFilterChange = (oldFilters, newFilters) => {
  // Check for changes that would affect the server query
  return (
    oldFilters.search !== newFilters.search ||
    !arraysEqual(oldFilters.priceRange, newFilters.priceRange) ||
    !arraysEqual(oldFilters.kilometersRange, newFilters.kilometersRange) ||
    !arraysEqual(oldFilters.yearRange, newFilters.yearRange) ||
    !arraysEqual(oldFilters.make, newFilters.make) ||
    !arraysEqual(oldFilters.model, newFilters.model) ||
    !arraysEqual(oldFilters.bodyType, newFilters.bodyType) ||
    !arraysEqual(oldFilters.transmission, newFilters.transmission) ||
    !arraysEqual(oldFilters.fuelType, newFilters.fuelType) ||
    !arraysEqual(oldFilters.color, newFilters.color) ||
    oldFilters.sortOption !== newFilters.sortOption
  );
};

// Helper to compare arrays
const arraysEqual = (a, b) => {
  if (!Array.isArray(a) || !Array.isArray(b) || a.length !== b.length) return false;
  return a.every((val, index) => val === b[index]);
};

// Update sort option - simplified for reliability
const updateSortOption = async (option) => {
  console.log('[Store] Updating sort option to:', option);
  
  // Update sort option
  activeFilters.value.sortOption = option;

  // If we already have vehicles loaded, just re-sort them client-side first
  // This provides an immediate response without waiting for server
  if (vehicles.value.length > 0) {
    console.log('[Store] Re-sorting existing vehicles client-side');
    
    // Create a copy to sort
    const sortedVehicles = [...vehicles.value];
    
    // Apply sorting based on option
    switch (option) {
      case 'newest':
        sortedVehicles.sort((a, b) => (b.year || 0) - (a.year || 0));
        break;
      case 'oldest':
        sortedVehicles.sort((a, b) => (a.year || 0) - (b.year || 0));
        break;
      case 'price-high':
        sortedVehicles.sort((a, b) => (b.price || 0) - (a.price || 0));
        break;
      case 'price-low':
        sortedVehicles.sort((a, b) => (a.price || 0) - (b.price || 0));
        break;
      case 'mileage-low':
        sortedVehicles.sort((a, b) => (a.mileage || 0) - (b.mileage || 0));
        break;
      case 'mileage-high':
        sortedVehicles.sort((a, b) => (b.mileage || 0) - (a.mileage || 0));
        break;
      default:
        sortedVehicles.sort((a, b) => (b.year || 0) - (a.year || 0));
    }
    
    // Update the vehicles with sorted list
    vehicles.value = sortedVehicles;
  }
  
  // Also do a server reload to ensure consistency
  isLoading.value = true;
  currentPage.value = 1;
  
  // Keep the current vehicles visible while loading
  hasMoreVehicles.value = true;
  lastPageLoaded.value = false;
  
  try {
    // Clear vehicles and reload from server
    vehicles.value = [];
    await loadNextPage();
    console.log('[Store] Successfully loaded vehicles with new sort option');
  } catch (error) {
    console.error('[Store] Error loading vehicles with new sort option:', error);
  } finally {
    isLoading.value = false;
  }

  return vehicles.value;
};

// Reset pagination state without changing filters
// This is used for recovery when loading fails
const resetPaginationState = async () => {
  console.log('[Store] Resetting pagination state for recovery');

  // Don't reset the current page to 1 - we want to try loading the current page again
  // But reset the other pagination flags
  hasMoreVehicles.value = true;
  lastPageLoaded.value = false;
  isLoading.value = false;

  // If we have no vehicles at all, reset to page 1
  if (vehicles.value.length === 0) {
    console.log('[Store] No vehicles loaded, resetting to page 1');
    currentPage.value = 1;
  } else {
    console.log(`[Store] Keeping current page at ${currentPage.value} for retry`);
  }

  // If we're on a page beyond 1 and have vehicles, we'll try to load the next page
  // If we're on page 1 with no vehicles, we'll try to load the first page again

  // Wait a moment before attempting to load again
  await new Promise(resolve => setTimeout(resolve, 500));

  try {
    // Don't await this - let the caller handle the loading state
    // This prevents potential deadlocks if the caller is waiting for this function
    loadNextPage();
    return true;
  } catch (error) {
    console.error('[Store] Error during pagination reset recovery:', error);
    return false;
  }
};

// Debug function to get total count of vehicles in the database
const getTotalVehicleCount = async () => {
  try {
    console.log('[Store] Getting total vehicle count from database...');
    const { count } = await supabase
      .from('GTINV')
      .select('*', { count: 'exact', head: true });

    console.log(`[Store] Total vehicles in database: ${count}`);
    return count;
  } catch (error) {
    console.error('[Store] Error getting total vehicle count:', error);
    return 0;
  }
};

// Export store interface
export default {
  // State
  vehicles,
  isLoading,
  isInitialized,
  useSupabase,
  currentPage,
  pageSize,
  totalVehicles,
  hasMoreVehicles,
  lastPageLoaded, // Add the new state
  activeFilters, // Export the filters

  // Actions
  initStore,
  loadNextPage,
  getAllVehicles, // Keep for backward compatibility
  getVehicleById,
  addVehicle,
  updateVehicle,
  deleteVehicle,
  vinLookup,
  updateFilters, // Add filter update method
  updateSortOption, // Add sort update method
  resetPaginationState, // Add recovery method
  getTotalVehicleCount, // Add debug function

  // Enhanced fetchVehicles to ensure data is loaded
  fetchVehicles: async () => {
    console.log('[Store] fetchVehicles called, ensuring store is initialized...');
    if (!isInitialized.value) {
        await initStore();
    } else if (vehicles.value.length === 0 && hasMoreVehicles.value) {
        // If store is initialized but empty and we think there's data, try loading again
        console.log('[Store] Store initialized but empty, attempting to load data...');
        await loadNextPage();
    } else {
        console.log('[Store] Store already initialized with data:', vehicles.value.length, 'vehicles');
    }
    return vehicles.value;
  },

  // Add a method to force refresh the store - IMPROVED
  refreshStore: async () => {
    console.log('[Store] Forcing store refresh...');

    // Reset all state
    isInitialized.value = false;
    isLoading.value = true;
    vehicles.value = [];
    currentPage.value = 1;
    totalVehicles.value = 0;
    hasMoreVehicles.value = true;
    lastPageLoaded.value = false;

    // Don't reset filters unless explicitly requested

    try {
      // Re-initialize the store
      await initStore();
      console.log('[Store] Store refresh completed successfully.');
      return vehicles.value;
    } catch (error) {
      console.error('[Store] Error during store refresh:', error);
      isLoading.value = false;
      throw error;
    }
  }
};